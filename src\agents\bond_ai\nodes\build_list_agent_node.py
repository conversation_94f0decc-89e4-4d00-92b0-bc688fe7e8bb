from agents.bond_ai.state import BondAIWorkflowState

from langchain_core.runnables import RunnableConfig

def build_list_agent_node(state: BondAIWorkflowState, config: RunnableConfig):
    """Build list agent node implementation."""
    from bond_ai.utils import clean_thinking_blocks_for_bedrock
    
    # Clean thinking blocks before processing
    cleaned_messages = clean_thinking_blocks_for_bedrock(list(state["messages"]))
    
    # Use cleaned messages in your model invocation
    # ... rest of your implementation